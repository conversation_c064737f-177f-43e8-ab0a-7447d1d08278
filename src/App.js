import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';

// 导入布局组件
import MainLayout from './layouts/MainLayout';

// 导入页面组件
import SparkTasksPage from './pages/SparkTasksPage';
import QueryAnalysisPage from './pages/QueryAnalysisPage';

function App() {
  return (
    <Router>
      <MainLayout>
        <Routes>
          <Route path="/spark-tasks" element={<SparkTasksPage />} />
          <Route path="/query-analysis" element={<QueryAnalysisPage />} />
          <Route path="/" element={<Navigate to="/spark-tasks" replace />} />
        </Routes>
      </MainLayout>
    </Router>
  );
}

export default App;
