import axios from 'axios';

let token = 'eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************.2DuqE9R91-bRG8krvLN68uiBNmEUKy-nKIAz93lVsp9UfIN6lT7PdRpq4hjCxYpugJMvAjjVv2-NepICv-NpzQ'

// 创建axios实例
const api = axios.create({
  baseURL: 'https://racingweb.devops.beta.xiaohongshu.com/web', // 实际应用中应该配置为后端API的基础URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': token,
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么，例如添加token
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data;
  },
  (error) => {
    // 对响应错误做点什么
    return Promise.reject(error);
  }
);

// Spark任务相关API
export const taskApi = {
  // 获取任务列表
  getTasks: () => api.get('/precompute/spark/configList'),
  switchEnv: (id, env) => api.post(`/precompute/spark/switchEnv?id=${id}&env=${env}`),
  switchReferType: (id, referType) => api.post(`/precompute/spark/switchReferType?id=${id}&referType=${referType}`),
  deleteConfig: (id) => api.delete(`/precompute/spark/deleteConfig?id=${id}`),
};

// 查询分析相关API
export const queryApi = {
  // 获取查询列表
  getQueries: () => api.get('/queries'),
  
  // 获取查询统计数据
  getQueryStats: () => api.get('/queries/stats'),
  
  // 获取推荐创建预计算的查询
  getRecommendedQueries: () => api.get('/queries/recommended'),
};

export default api;
