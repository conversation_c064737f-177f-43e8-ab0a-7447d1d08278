import React, { useState, useEffect } from 'react';
import { Table, Button, Form, Input, message, Tag, Modal } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { taskApi } from '../../services/api';

const TaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [filterMetricTables, setFilterMetricTables] = useState([]);
  const [filterConditions, setFilterConditions] = useState([]);
  const [filterMetricTypes, setFilterMetricTypes] = useState([]);
  const [filterRefTypes, setFilterRefTypes] = useState([]);
  const [filterExpTypes, setFilterExpTypes] = useState([]);
  const [filterStatuses, setFilterStatuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTasks();
  }, []);

  // 通用函数，用于生成唯一的过滤选项
  function generateUniqueFilters(data, fieldName) {
    const uniqueSet = new Set();
    return data.map(item => {
      const value = item[fieldName];
      if (value && !uniqueSet.has(value)) {
        uniqueSet.add(value);
        return { text: value, value: value };
      }
      return null;
    }).filter(item => item !== null);
  }

  const fetchTasks = () => {
    setLoading(true);
    taskApi.getTasks().then(response => {
      const data = response.data
      setTasks(data);
      // 设置各列的过滤选项
      setFilterMetricTables(generateUniqueFilters(data, 'metricTable'));
      setFilterConditions(generateUniqueFilters(data, 'filterDesc'));
      setFilterMetricTypes(generateUniqueFilters(data, 'metricType'));
      setFilterRefTypes(generateUniqueFilters(data, 'refType'));
      setFilterExpTypes(generateUniqueFilters(data, 'expType'));
      setFilterStatuses(generateUniqueFilters(data, 'status'));
      setLoading(false);
    });
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleCreate = () => {
    form.validateFields()
      .then(values => {
        // 在实际应用中，这里应该调用API来创建新任务
        const newTask = {
          id: tasks.length + 1,
          name: values.name,
          status: true,
          lastRunTime: '刚刚创建',
          hitCount: 0,
          query: values.query,
        };

        setTasks([...tasks, newTask]);
        form.resetFields();
        setIsModalVisible(false);
        message.success('任务创建成功');
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '任务说明',
      dataIndex: 'memo',
      key: 'memo',
      ellipsis: true,
      width: 120,
    },
    {
      title: '筛选条件',
      dataIndex: 'filterDesc',
      key: 'filterDesc',
      width: 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
      filters: filterConditions,
      onFilter: (value, record) => record.filterDesc === value,
    },
    {
      title: '指标',
      dataIndex: 'metricType',
      key: 'metricType',
      width: 120,
      ellipsis: true,
      render: (text) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
      filters: filterMetricTypes,
      onFilter: (value, record) => record.metricType === value,
    },
    {
      title: '依赖',
      dataIndex: 'refType',
      key: 'refType',
      width: 80,
      render: (val, record) => (
        <Tag
          color={val === '强' ? 'red' : 'blue'}
          style={{ cursor: 'pointer' }}
          onClick={() => {
            const newRefType = val === '强' ? '弱' : '强';
            const newRefTypeArg = newRefType === '强' ? 'true' : 'false';
            Modal.confirm({
              title: '切换依赖类型',
              content: `确定要将依赖类型从"${val}"切换为"${newRefType}"吗？`,
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                setLoading(true);
                taskApi.switchReferType(record.id, newRefTypeArg)
                  .then(response => {
                    if (response && response.code == 200) {
                      setTasks(
                        tasks.map(task =>
                          task.id === record.id ? { ...task, refType: newRefType } : task
                        )
                      );
                      message.success(`依赖类型已切换为"${newRefType}"`);
                    } else {
                      message.error('切换依赖类型失败');
                    }
                    setLoading(false);
                  })
                  .catch(error => {
                    console.error('切换依赖类型出错:', error);
                    message.error('切换依赖类型出错');
                    setLoading(false);
                  });
              }
            });
          }}
        >
          {val}
        </Tag>
      ),
      filters: filterRefTypes,
      onFilter: (value, record) => record.refType === value,
    },
    {
      title: '实验',
      dataIndex: 'expType',
      key: 'expType',
      width: 80,
      render: (val) => (
        <Tag color={val === '服务端' ? 'blue' : 'green'}>
          {val}
        </Tag>
      ),
      filters: filterExpTypes,
      onFilter: (value, record) => record.expType === value,
    },
    {
      title: '命中次数',
      dataIndex: 'hitCount',
      key: 'hitCount',
      sorter: (a, b) => a.hitCount - b.hitCount,
      width: 120,
    },
    {
      title: <span style={{ fontSize: '12px' }}>指标表</span>,
      dataIndex: 'metricTable',
      key: 'metricTable',
      ellipsis: true,
      width: 240,
      render: (text, record) => {
        const style = { fontSize: '10px' }
        if (!record.md5Correct) {
          style.color = 'red'
        }
        return <span style={style}>{text}</span>
      },
      filters: filterMetricTables,
      onFilter: (value, record) => record.metricTable === value,
    },
    {
      title: '最新分区',
      dataIndex: 'latestTaskDtm',
      key: 'latestTaskDtm',
      sorter: (a, b) => a.latestTaskDtm - b.latestTaskDtm,
      width: 120,
      render: (val, record) => (
        <Tag
          color={record.latestTaskCreated ? 'green' : 'red'}
        >
          {val}
        </Tag>
      ),
    },
    {
      title: '完成时间',
      dataIndex: 'latestTaskCompleteTime',
      key: 'latestTaskCompleteTime',
      sorter: (a, b) => a.latestTaskCompleteTime.localeCompare(b.latestTaskCompleteTime),
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (val, record) => {
        const newEnv = val === '线上' ? 'beta' : '线上';
        const newEnvArg = newEnv === '线上' ? 'prod' : 'staging';
        return (
          <Tag
            color={val === '线上' ? 'green' : 'blue'}
            style={{ cursor: 'pointer' }}
            onClick={() => {
              Modal.confirm({
                title: '切换状态',
                content: `确定要将状态从"${val}"切换为"${newEnv}"吗？`,
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  setLoading(true);
                  taskApi.switchEnv(record.id, newEnvArg)
                    .then(response => {
                      if (response && response.code == 200) {
                        setTasks(
                          tasks.map(task =>
                            task.id === record.id ? { ...task, status: newEnv } : task
                          )
                        );
                        message.success(`状态已切换为"${newEnv}"`);
                      } else {
                        message.error('切换状态失败');
                      }
                      setLoading(false);
                    })
                    .catch(error => {
                      console.error('切换状态出错:', error);
                      message.error('切换状态出错');
                      setLoading(false);
                    });
                }
              });
            }}
          >
            {val}
          </Tag>
        );
      },
      filters: filterStatuses,
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      render: (_, record) => (
        <Button
          type="link"
          danger
          onClick={() => {
            Modal.confirm({
              title: '删除任务',
              content: `确定要删除任务吗？此操作不可恢复！`,
              okText: '确认删除',
              okType: 'danger',
              cancelText: '取消',
              onOk: () => {
                setLoading(true);
                taskApi.deleteConfig(record.id)
                  .then(response => {
                    if (response && response.code == 200) {
                      setTasks(tasks.filter(task => task.id !== record.id));
                      message.success('任务已成功删除');
                    } else {
                      message.error('删除任务失败');
                    }
                    setLoading(false);
                  })
                  .catch(error => {
                    console.error('删除任务出错:', error);
                    message.error('删除任务出错');
                    setLoading(false);
                  });
              }
            });
          }}
        >
          删除
        </Button>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showModal}
          style={{ marginRight: 8 }}
        >
          创建新任务
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchTasks}
        >
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={tasks}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title="创建新的Spark预计算任务"
        open={isModalVisible}
        onOk={handleCreate}
        onCancel={handleCancel}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          name="form_in_modal"
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>
          <Form.Item
            name="query"
            label="OLAP查询语句"
            rules={[{ required: true, message: '请输入OLAP查询语句' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入OLAP查询语句" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskList;
