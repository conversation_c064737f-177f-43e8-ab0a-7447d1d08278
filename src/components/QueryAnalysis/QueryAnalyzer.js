import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Row, Col, Statistic, Tag, Space, Modal, message } from 'antd';
import { BarChartOutlined, LineChartOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons';

// 模拟数据，实际应用中应该从API获取
const mockQueries = [
  {
    id: 1,
    query: 'SELECT date, SUM(sales) FROM daily_sales GROUP BY date ORDER BY date',
    frequency: 245,
    avgDuration: 3.2,
    lastExecuted: '2023-11-10 16:45:22',
    recommended: true,
  },
  {
    id: 2,
    query: 'SELECT category, COUNT(*) FROM products GROUP BY category',
    frequency: 189,
    avgDuration: 1.8,
    lastExecuted: '2023-11-10 16:40:15',
    recommended: false,
  },
  {
    id: 3,
    query: 'SELECT customer_id, SUM(order_value) FROM orders GROUP BY customer_id ORDER BY SUM(order_value) DESC LIMIT 100',
    frequency: 156,
    avgDuration: 5.7,
    lastExecuted: '2023-11-10 16:38:30',
    recommended: true,
  },
  {
    id: 4,
    query: 'SELECT region, product_id, SUM(quantity) FROM sales GROUP BY region, product_id',
    frequency: 132,
    avgDuration: 4.3,
    lastExecuted: '2023-11-10 16:35:12',
    recommended: true,
  },
  {
    id: 5,
    query: 'SELECT hour, AVG(response_time) FROM api_logs GROUP BY hour',
    frequency: 98,
    avgDuration: 2.1,
    lastExecuted: '2023-11-10 16:30:45',
    recommended: false,
  },
];

const QueryAnalyzer = () => {
  const [queries, setQueries] = useState([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalQueries: 0,
    avgDuration: 0,
    recommendedCount: 0,
  });

  useEffect(() => {
    fetchQueries();
  }, []);

  const fetchQueries = () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setQueries(mockQueries);

      // 计算统计数据
      const totalQueries = mockQueries.length;
      const avgDuration = mockQueries.reduce((sum, q) => sum + q.avgDuration, 0) / totalQueries;
      const recommendedCount = mockQueries.filter(q => q.recommended).length;

      setStats({
        totalQueries,
        avgDuration,
        recommendedCount,
      });

      setLoading(false);
    }, 500);
  };

  const handleCreateTask = (query) => {
    Modal.confirm({
      title: '创建预计算任务',
      content: `确定要为以下查询创建预计算任务吗？\n${query.query}`,
      onOk() {
        // 在实际应用中，这里应该调用API来创建新任务
        message.success('已创建预计算任务');
      },
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '查询语句',
      dataIndex: 'query',
      key: 'query',
      ellipsis: true,
    },
    {
      title: '执行频率',
      dataIndex: 'frequency',
      key: 'frequency',
      sorter: (a, b) => a.frequency - b.frequency,
      render: (frequency) => `${frequency} 次/天`,
    },
    {
      title: '平均执行时间',
      dataIndex: 'avgDuration',
      key: 'avgDuration',
      sorter: (a, b) => a.avgDuration - b.avgDuration,
      render: (duration) => `${duration} 秒`,
    },
    {
      title: '最后执行时间',
      dataIndex: 'lastExecuted',
      key: 'lastExecuted',
    },
    {
      title: '推荐创建预计算',
      dataIndex: 'recommended',
      key: 'recommended',
      render: (recommended) => (
        recommended ?
          <Tag color="green">推荐</Tag> :
          <Tag color="default">不推荐</Tag>
      ),
      filters: [
        { text: '推荐', value: true },
        { text: '不推荐', value: false },
      ],
      onFilter: (value, record) => record.recommended === value,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="small"
            onClick={() => handleCreateTask(record)}
          >
            创建任务
          </Button>
          <Button type="link" size="small">查看详情</Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchQueries}
        >
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={queries}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />
    </div>
  );
};

export default QueryAnalyzer;
